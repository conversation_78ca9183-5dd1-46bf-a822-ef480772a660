<template>
  <!-- 分析表单弹窗 -->
  <Dialog
      :title="dialogTitle" v-model="dialogVisible" width="85%" height="650"
      :loading="formLoading" :border="true" 
      class="analysis-dialog">
    
          <!-- 订单选择部分 -->
      <el-card class="order-selection-card" shadow="hover" v-if="!showAnalysisResult">
        <template #header>
          <div class="selection-header">
            <div class="header-title">
              <el-icon><List /></el-icon>
              <span>选择需要分析的订单</span>
            </div>
            <div class="header-action">
              <el-button type="primary" link underline @click="viewResult">
                <el-icon><Reading /></el-icon> 查看上次分析结果
              </el-button>
              <el-button type="primary" @click="submitForm" :disabled="formLoading || selectedItems.length === 0">
                <el-icon><Search /></el-icon> 开始分析
              </el-button>
            </div>
          </div>
        </template>
      
        <el-table 
          v-loading="loading" 
          :data="list" 
          :stripe="true" 
          size="default"
          border
          :show-overflow-tooltip="true"
          @selection-change="handleSelectionChange"
          class="order-selection-table"
          row-key="id"
          ref="tableRef"
          >
          <el-table-column type="selection" width="45" align="center" />
          <!-- 移除了物料明细展开部分 -->
          <el-table-column label="序号" align="center" type="index" width="60" />
          <el-table-column label="订单编号" align="center" prop="orderNo" width="120"/>
          <el-table-column label="物料编号" align="center" prop="productCode" width="120" />
          <el-table-column label="物料名称" align="left" prop="productName" width="120"/>
          <el-table-column label="规格" align="center" prop="spec" width="80"/>
          <el-table-column label="需求数量" align="center" prop="orderQuantity" width="100">
            <template #default="scope">
                <span v-if="scope.row.orderUnit">{{ scope.row.orderQuantity }} {{ unitMap?.get(String(scope.row.orderUnit)) || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="未转任务" align="center" prop="pendingQuantity" width="100"/>
          <el-table-column label="分析数量" align="center" width="170">
            <template #default="scope">
              <div class="quantity-editor">
                <el-input-number 
                  v-model="scope.row.analysisQuantity" 
                  :min="0" 
                  :max="scope.row.pendingQuantity" 
                  size="small"
                  :precision="3"
                  :step="0.001"
                  controls-position="right" />
                <span class="unit-text">{{ unitMap?.get(String(scope.row.orderUnit)) || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="库存可生产" align="center" prop="readyQuantity" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.readyQuantity <= 0 ? 'danger' : 'success'" size="small">
                {{ scope.row.readyQuantity }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="准备状态" align="center" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MFG_MATERIAL_READY_STATUS" :value="scope.row.readyStatus" />
            </template>
          </el-table-column>
          <el-table-column label="上次分析时间" align="center" prop="lastAnalysisTime" width="120" :formatter="dateFormatter"/>
          <el-table-column label="上次分析人" align="center" prop="lastAnalysisUser"  width="120" />
          <el-table-column label="要求" align="center" min-width="120">
            <template #default="scope">
                <div class="remark-text">
              <span v-if="scope.row.requirement">{{ scope.row.requirement }}</span>
              <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
                </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

    <!-- 分析结果 -->
    <AnalysisResult
      v-if="showAnalysisResult"
      :analysis-list="analysisList"
      :analysis-summary="analysisSummary"
      :unit-map="unitMap"
      :loading="loading"
      @back="showAnalysisResult = false"
      @transfer-requirement="transferRequirement"
      @transfer-work-order="handleSingleOrderTransfer"
      @summary-transfer-requirement="transferRequirement"
    />
    
    <!-- 弹窗底部按钮 -->
    <template #footer>
      <el-button @click="cancelDialog">关 闭</el-button>
    </template>
    
    <!-- 关联表单组件 -->
    <RequirementForm ref="requirementFormRef" @success="handleRequirementSuccess" />
    <WorkOrderForm ref="workOrderFormRef" @success="handleWorkOrderSuccess" />
  </Dialog>
</template>
<script setup lang="ts">
import { ElLoading, ElIcon, ElMessageBox } from 'element-plus'
import { List, Reading, Search } from '@element-plus/icons-vue'
import { DICT_TYPE } from '@/utils/dict'
import { defineExpose, reactive } from 'vue'
import { dateFormatter } from '@/utils/formatTime'
import { RequestOrderApi } from '@/api/scm/mfg/requestorder'
import { WarehouseApi } from '@/api/scm/inventory/warehouse'
import { MaterialApi } from '@/api/scm/base/material'

import { UnitApi } from '@/api/scm/base/unit'
import WorkOrderForm from '@/views/scm/mfg/workorder/WorkOrderForm.vue'
import RequirementForm from '@/views/scm/purchase/requirement/RequirementForm.vue'
import AnalysisResult from './AnalysisResult.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const loading = ref(false) // 列表的加载中
const transferLoading = ref(false) // 转单加载状态
const list = ref<any[]>([]) // 列表的数据
const selectedItems = ref<any[]>([]) // 选中的订单项
const analysisList = ref<any[]>([]) // 分析结果列表的数据
const showAnalysisResult = ref(false) // 是否显示分析结果

// 仓库和单位Map
const warehouseMap = ref<Map<string, string>>(new Map()) // 初始化默认值
const unitMap = ref<Map<string, string>>(new Map()) // 单位Map
const tableRef = ref()

// 分析总结数据
const analysisSummary = reactive({
  orderCount: 0,
  materialCount: 0,
  shortageCount: 0,
  readyCount: 0,
  partialReadyCount: 0,
  notReadyCount: 0,
  purchasedCount: 0
})



// 表格选择变更
const handleSelectionChange = (val: any[]) => {
  selectedItems.value = val
  
  // 为选择的每个订单设置默认分析数量
  selectedItems.value.forEach(item => {
    if (item.pendingQuantity > 0 && !item.analysisQuantity) {
      item.analysisQuantity = item.pendingQuantity
    }
  })
}

// 移除了处理展开行事件、获取订单物料明细和同步订单数量到明细的函数

// 移除了明细表格选择变更函数



/** 打开弹窗 */
const open = async (type: string, ids?: number[]) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  showAnalysisResult.value = false
  selectedItems.value = []

  // 清理缓存，确保使用最新数据
  materialCache.clear()
  unitCache.value.clear()

  // 修改时，设置数据
  if (ids && ids.length > 0) {
    formLoading.value = true
    try {
      // 调用API获取订单主数据
      // @ts-ignore - 忽略类型检查，按照API实际期望的参数格式传递
      list.value = await RequestOrderApi.getRequestOrderByIds(ids)

      // 初始化分析数量
      list.value.forEach(item => {
        item.analysisQuantity = item.pendingQuantity
      })
      tableRef.value.toggleAllSelection(true)
      // 移除了预先加载物料明细的逻辑
    } catch (error) {

      message.error('获取订单数据失败')
    } finally {
      formLoading.value = false
    }
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交分析 */
const submitForm = async () => {
  if (selectedItems.value.length === 0) {
    message.warning('请选择需要分析的订单')
    return
  }
  
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '分析中...',
    background: 'rgba(0, 0, 0, 0.3)'
  })

  try {
    // 构建分析参数，只包含订单ID和分析数量
    const analysisParam = selectedItems.value.map((item) => {
      // 构建基本的订单参数
      const orderParam: any = {
        id: item.id,
        quantity: item.analysisQuantity || item.pendingQuantity
      }
      
      return orderParam
    })

    const res = await RequestOrderApi.analysis({ details: analysisParam })
    // 查询分析结果
    if (res) {
      // 获取selectedItems中每个订单的ID
      const orderIds = selectedItems.value.map(item => item.id)
      const analysisResult = await RequestOrderApi.getAnalysisResult(orderIds)

      // 处理分析结果，转换采购数量单位
      const processedAnalysisResult = await processAnalysisResultWithUnitConversion(analysisResult || [])
      analysisList.value = processedAnalysisResult

      showAnalysisResult.value = true
      // 计算分析总结数据
      calculateAnalysisSummary()
      message.success('分析完成')
    } else {
      message.error('分析失败，请重试')
    }
  } catch (error) {
    message.error('分析失败，请重试')
  } finally {
    loadingInstance.close()
  }
}

const viewResult = async () => {
  const orderIds = selectedItems.value.map(item => item.id)
  const analysisResult = await RequestOrderApi.getAnalysisResult(orderIds)

  // 处理分析结果，转换采购数量单位
  const processedAnalysisResult = await processAnalysisResultWithUnitConversion(analysisResult || [])
  analysisList.value = processedAnalysisResult

  showAnalysisResult.value = true
  // 计算分析总结数据
  calculateAnalysisSummary()
}
// 计算分析总结数据
const calculateAnalysisSummary = () => {
  // 重置所有计数器
  analysisSummary.orderCount = analysisList.value.length
  analysisSummary.materialCount = 0
  analysisSummary.shortageCount = 0
  analysisSummary.readyCount = 0
  analysisSummary.notReadyCount = 0
  analysisSummary.partialReadyCount = 0
  analysisSummary.purchasedCount = 0

  let materialCount = 0
  let shortageCount = 0
  let readyCount = 0
  let notReadyCount = 0
  let purchasedCount = 0

  analysisList.value.forEach(order => {
    const details = order.details || []
    materialCount += details.length

    // 统计缺料项和准备状态
    details.forEach((detail: any) => {
      // 缺料统计
      if (detail.shortageQuantity < 0) {
        shortageCount++
      }

      // 已转采购统计
      if (detail.purchaseQuantity && detail.purchaseQuantity > 0) {
        purchasedCount++
      }

      // 准备状态统计 - 使用数值
      if (detail.readyStatus === 1) { // 可用
        readyCount++
      } else if (detail.readyStatus === 0) { // 不可用
        notReadyCount++
      } else if (detail.readyStatus === 2) { // 部分可用
        analysisSummary.partialReadyCount++
      }
    })
  })

  analysisSummary.materialCount = materialCount
  analysisSummary.shortageCount = shortageCount
  analysisSummary.readyCount = readyCount
  analysisSummary.notReadyCount = notReadyCount
  analysisSummary.purchasedCount = purchasedCount
  // 如果没有明确的部分准备状态，则计算剩余的物料为部分准备
  if (analysisSummary.partialReadyCount === 0) {
    analysisSummary.partialReadyCount = materialCount - readyCount - notReadyCount
  }
}

// 处理单个订单的转生产
const handleSingleOrderTransfer = (order: any) => {
  // 检查订单准备状态
  if (order.readyStatus === 0) { // 不可用
    ElMessageBox.confirm(
      '该订单物料准备不足，是否继续转生产？',
      '库存不足提醒',
      {
        confirmButtonText: '继续转生产',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 用户确认继续
      proceedTransferWorkOrder(order)
    }).catch(() => {
      // 用户取消操作
      message.info('已取消转生产')
    })
  } else {
    // 物料准备充足，直接处理
    proceedTransferWorkOrder(order)
  }
}

// 执行转生产操作
const proceedTransferWorkOrder = async (order: any) => {
  transferLoading.value = true
  try {
    // 确保订单数据完整
    if (!order || !order.id) {
      message.error('订单数据不完整，无法转生产')
      return
    }

    // 设置默认的计划时间范围（当前时间到交货日期）
    const now = Date.now()
    const deliverTime = order.deliverDate ? new Date(order.deliverDate).getTime() : now + 7 * 24 * 60 * 60 * 1000 // 默认7天后

    // 计算转换后的生产数量（考虑产品制造单位）
    const originalQuantity = order.pendingQuantity || order.orderQuantity
    const convertedQuantity = await convertOrderToMfgQuantity(
      originalQuantity,
      order.productUnit, // 需求订单的产品单位
      order.productId    // 产品ID，用于获取制造单位
    )

    // 构建完整的订单数据
    const transferData = {
      // 订单基本信息
      id: order.id,
      requestNo: order.requestNo,
      orderType: 'mfg_request_order', // 指定来源类型为生产需求订单

      // 产品信息
      productId: order.productId,
      productCode: order.productCode,
      productName: order.productName,
      productUnit: order.productUnit,
      productSubType: order.productSubType,
      spec: order.spec,

      // 数量信息（使用转换后的数量）
      orderQuantity: convertedQuantity,
      scheduleQuantity: convertedQuantity, // 计划数量使用转换后的数量
      
      // 订单信息
      orderId: order.orderId,
      orderNo: order.orderNo,
      
      // 客户信息
      customerId: order.customerId,
      customerName: order.customerName,
      
      // 日期信息
      orderDate: order.orderDate,
      deliverDate: order.deliverDate,
      scheduleTimeRange: [now, deliverTime],
      scheduleStartDate: now,
      scheduleEndDate: deliverTime,
      
      // BOM信息
      bomId: order.bomId,
      bomCode: order.bomCode,
      bomVersion: order.bomVersion,
      
      // 其他信息
      requirement: order.requirement,
      remark: order.remark,
      
      // 物料明细
      details: order.details || []
    }
    

    
    // 打开工单表单
    if (workOrderFormRef.value) {
      workOrderFormRef.value.open('transferMfg', transferData)
    } else {
      message.error('工单表单组件未初始化')
    }
  } catch (error) {
    message.error('转生产失败，请重试')
  } finally {
    transferLoading.value = false
  }
}

const requirementFormRef = ref<InstanceType<typeof RequirementForm> | null>(null)

// 转采购需求成功回调
const handleRequirementSuccess = async () => {
  message.success('转采购需求成功')
  // 刷新分析结果数据
  await refreshAnalysisResult()
}

// 转生产成功回调
const handleWorkOrderSuccess = async () => {
  message.success('转生产成功')
  // 刷新分析结果数据
  await refreshAnalysisResult()
}

// 刷新分析结果数据
const refreshAnalysisResult = async () => {
  try {
    // 获取当前选中订单的ID
    const orderIds = selectedItems.value.map(item => item.id)
    if (orderIds.length === 0) {
      return
    }

    // 重新获取分析结果
    const analysisResult = await RequestOrderApi.getAnalysisResult(orderIds)

    // 处理分析结果，转换采购数量单位
    const processedAnalysisResult = await processAnalysisResultWithUnitConversion(analysisResult || [])
    analysisList.value = processedAnalysisResult

    // 重新计算分析总结数据
    calculateAnalysisSummary()
  } catch (error) {
    console.error('刷新分析结果失败:', error)
    message.error('刷新分析结果失败，请重试')
  }
}

// 转采购需求
const transferRequirement = async (row: any) => {
  // 判断数据来源：是来自分析详情tab还是分析总结tab
  const isFromSummary = !row.detail && row.materialId;

  // 如果是来自分析详情tab
  if (!isFromSummary && (!row.detail || !row.detail.materialId)) {
    message.warning('无效的物料数据')
    return
  }

  // 如果是来自分析总结tab
  if (isFromSummary && !row.materialId) {
    message.warning('无效的物料数据')
    return
  }

  // 根据数据来源获取物料ID和其他信息
  const materialId = isFromSummary ? row.materialId : row.detail.materialId;
  const materialCode = isFromSummary ? row.materialCode : row.detail.materialCode;
  const materialName = isFromSummary ? row.materialName : row.detail.materialName;
  const materialType = isFromSummary ? row.materialType : row.detail.materialType;
  const materialSpec = isFromSummary ? row.spec : row.detail.spec;
  const materialUnit = isFromSummary ? row.unit : row.detail.unit;
  const shortageQuantity = isFromSummary ? row.shortageQuantity : row.detail.shortageQuantity;
  const pendingQuantity = isFromSummary ? Math.abs(shortageQuantity || 0) : row.detail.pendingQuantity;

  // 使用缓存获取物料的采购单位
  const material = await getMaterialWithCache(materialId)

  // 计算转换后的数量
  const originalQuantity = Math.abs(shortageQuantity || 0) > pendingQuantity ? pendingQuantity : Math.abs(shortageQuantity || 0)
  const quantity = await convertPurchaseQuantity(
    originalQuantity,
    material.purchaseUnit,
    materialUnit
  )



  // 构建预填充数据
  const prefilledData = {
    materialId: materialId,
    materialCode: materialCode,
    materialName: materialName,
    materialType: materialType,
    materialSpec: materialSpec,
    requirementDate: new Date().getTime(),
    unit: material.purchaseUnit,
    quantity: quantity,
    sourceType: 'mfg_request_order',
    // 如果是来自分析总结tab，这些字段可能为空
    sourceId: isFromSummary ? null : row.id,
    sourceNo: isFromSummary ? null : row.requestNo,
    sourceDetailId: isFromSummary ? null : row.detail.id,
    bizOrderType: 'sale_order',
    bizOrderId: isFromSummary ? null : row.orderId,
    bizOrderNo: isFromSummary ? null : row.orderNo,
    detailId: isFromSummary ? null : row.detail.id
  }

  requirementFormRef.value?.open('transferPurchase', undefined, prefilledData)
}

// 校验生产的单位，采购的单位，如果不一致，就按照转换率进行转换
const convertPurchaseQuantity = async (quantity: number, purchaseUnit: number, mfgUnit: number) => {
  if (!purchaseUnit || !mfgUnit) {
    return quantity
  }

  const unitVOMap = await getUnitsWithCache()
  const purchaseUnitVO = unitVOMap.get(purchaseUnit) as any
  const mfgUnitVO = unitVOMap.get(mfgUnit) as any

  // 检查单位是否相同
  if (purchaseUnitVO?.id === mfgUnitVO?.id) {
    return quantity
  }

  // 正确的换算公式：生产单位数量 * 生产单位系数 / 采购单位系数 = 采购单位数量
  // 例如：4000千克 * 1 / 1000 = 4吨
  const mfgCoefficient = mfgUnitVO?.coefficient || 1
  const purchaseCoefficient = purchaseUnitVO?.coefficient || 1
  const convertedValue = quantity * mfgCoefficient / purchaseCoefficient

  // 保留4位小数，并去掉末尾的0
  return parseFloat(convertedValue.toFixed(4))
}

// 将采购单位的数量转换为生产单位的数量（用于显示比较）
// 这是 convertPurchaseQuantity 的逆向操作
const convertPurchaseToMfgQuantity = async (purchaseQuantity: number, purchaseUnit: number, mfgUnit: number) => {
  if (!purchaseUnit || !mfgUnit || purchaseQuantity === 0) {
    return purchaseQuantity
  }

  const unitVOMap = await getUnitsWithCache()

  const purchaseUnitVO = unitVOMap.get(purchaseUnit) as any
  const mfgUnitVO = unitVOMap.get(mfgUnit) as any

  // 检查单位是否相同
  if (purchaseUnitVO?.id === mfgUnitVO?.id) {
    return purchaseQuantity
  }

  // 正确的换算公式：采购单位数量 * 采购单位系数 / 生产单位系数 = 生产单位数量
  // 例如：4吨 * 1000 / 1 = 4000千克
  const mfgCoefficient = mfgUnitVO?.coefficient || 1
  const purchaseCoefficient = purchaseUnitVO?.coefficient || 1
  const convertedValue = purchaseQuantity * purchaseCoefficient / mfgCoefficient

  return parseFloat(convertedValue.toFixed(4))
}

// 使用缓存的单位转换函数
const convertPurchaseToMfgQuantityWithCache = async (purchaseQuantity: number, purchaseUnit: number, mfgUnit: number) => {
  if (!purchaseUnit || !mfgUnit || purchaseQuantity === 0) {
    return purchaseQuantity
  }

  const unitVOMap = await getUnitsWithCache()
  const purchaseUnitVO = unitVOMap.get(purchaseUnit) as any
  const mfgUnitVO = unitVOMap.get(mfgUnit) as any

  // 检查单位是否相同
  if (purchaseUnitVO?.id === mfgUnitVO?.id) {
    return purchaseQuantity
  }

  // 正确的换算公式：采购单位数量 * 采购单位系数 / 生产单位系数 = 生产单位数量
  // 例如：4吨 * 1000 / 1 = 4000千克
  const purchaseCoefficient = purchaseUnitVO?.coefficient || 1
  const mfgCoefficient = mfgUnitVO?.coefficient || 1

  const convertedValue = (purchaseQuantity * purchaseCoefficient) / mfgCoefficient

  // 保留4位小数
  return parseFloat(convertedValue.toFixed(4))
}

// 将需求订单的数量转换为生产工单的数量（考虑产品的制造单位）
const convertOrderToMfgQuantity = async (orderQuantity: number, orderUnit: number, productId: number) => {
  if (!productId || orderQuantity === 0) {
    return orderQuantity
  }

  try {
    // 使用缓存获取产品信息以获取制造单位
    const material = await getMaterialWithCache(productId)

    if (!material?.mfgUnit || !orderUnit) {
      return orderQuantity
    }

    // 如果需求订单单位与产品制造单位相同，直接返回
    if (orderUnit === material.mfgUnit) {
      return orderQuantity
    }

    // 使用缓存进行单位换算
    const unitVOMap = await getUnitsWithCache()
    const orderUnitVO = unitVOMap.get(orderUnit) as any
    const mfgUnitVO = unitVOMap.get(material.mfgUnit) as any

    if (!orderUnitVO || !mfgUnitVO) {
      return orderQuantity
    }

    // 换算公式：订单数量 * 订单单位系数 / 制造单位系数 = 制造单位数量
    const orderCoefficient = orderUnitVO.coefficient || 1
    const mfgCoefficient = mfgUnitVO.coefficient || 1
    const convertedValue = orderQuantity * orderCoefficient / mfgCoefficient

    return parseFloat(convertedValue.toFixed(4))
  } catch (error) {
    console.error('转换生产数量单位失败:', error)
    return orderQuantity
  }
}

// 缓存物料信息和单位信息，避免重复调用
const materialCache = new Map<number, any>()
const unitCache = ref<Map<number, any>>(new Map())

// 获取物料信息（带缓存）
const getMaterialWithCache = async (materialId: number) => {
  if (materialCache.has(materialId)) {
    return materialCache.get(materialId)
  }

  try {
    const materialRes = await MaterialApi.getSimpleMaterialPage({ id: materialId })
    const material = materialRes.list[0]
    if (material) {
      materialCache.set(materialId, material)
    }
    return material
  } catch (error) {
    console.error('获取物料信息失败:', error)
    return null
  }
}

// 获取单位信息（带缓存）
const getUnitsWithCache = async () => {
  if (unitCache.value.size > 0) {
    return unitCache.value
  }

  try {
    const res = await UnitApi.getUnitPage({
      pageNo: 1,
      pageSize: 100
    })
    const unitVOMap = new Map(res.list.map((item: any) => [item.id, item]))
    unitCache.value = unitVOMap
    return unitVOMap
  } catch (error) {
    console.error('获取单位信息失败:', error)
    return new Map()
  }
}

// 处理分析结果，转换采购数量单位以便正确比较
const processAnalysisResultWithUnitConversion = async (analysisResult: any[]): Promise<any[]> => {
  const processedResult: any[] = []

  // 预先获取单位信息
  await getUnitsWithCache()

  for (const order of analysisResult) {
    const processedOrder = { ...order }

    if (order.details && Array.isArray(order.details)) {
      const processedDetails: any[] = []

      for (const detail of order.details) {
        const processedDetail = { ...detail }

        // 如果有采购数量且大于0，需要转换单位
        if (detail.purchaseQuantity && detail.purchaseQuantity > 0 && detail.materialId) {
          try {
            // 使用缓存获取物料的采购单位
            const material = await getMaterialWithCache(detail.materialId)

            if (material?.purchaseUnit && detail.unit) {
              // 将采购单位的数量转换为生产单位的数量，用于按钮显示判断
              const convertedPurchaseQuantity = await convertPurchaseToMfgQuantityWithCache(
                detail.purchaseQuantity,
                material.purchaseUnit,
                detail.unit
              )

              // 添加转换后的数量字段，用于按钮显示判断
              processedDetail.convertedPurchaseQuantity = convertedPurchaseQuantity
            }
          } catch (error) {
            console.error('转换采购数量单位失败:', error)
          }
        }

        processedDetails.push(processedDetail)
      }

      processedOrder.details = processedDetails
    }

    processedResult.push(processedOrder)
  }

  return processedResult
}



const workOrderFormRef = ref<InstanceType<typeof WorkOrderForm> | null>(null)

// 关闭弹窗
const cancelDialog = () => {
  dialogVisible.value = false
}

// 获取仓库列表
const getWarehouseMap = async () => {
  try {
  const res = await WarehouseApi.getWarehouseList({
    pageNo: 1,
    pageSize: 100
  })
    warehouseMap.value = new Map(res.map((item: any) => [String(item.id), item.name]))
  } catch (error) {
    console.error('获取仓库列表失败')
  }
}



// 获取单位map
const getUnitMap = async () => {
  try {
    const unitVOMap = await getUnitsWithCache()
    unitMap.value = new Map(Array.from(unitVOMap.entries()).map(([id, item]) => [String(id), item.name]))
  } catch (error) {
    console.error('获取单位列表失败')
  }
}

/** 初始化 **/
onMounted(async () => {
  await getUnitMap()
  await getWarehouseMap()
})
</script>

<style lang="scss" scoped>
.analysis-dialog {
  :deep(.el-dialog__body) {
    padding: 10px;
    overflow: hidden;
  }
  
  :deep(.el-dialog__body .el-scrollbar__wrap) {
    overflow-x: hidden;
  }
}

// 订单选择卡片样式
.order-selection-card {
  margin-bottom: 20px;
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    background-color: #f9fafc;
  }
  
  :deep(.el-card__body) {
    padding: 0;
  }
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .header-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    
    .el-icon {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}



.order-selection-table {
  :deep(.el-table__header) {
    background-color: #f5f7fa;
  }

  :deep(.el-table--small) {
    font-size: 14px;
  }

  :deep(.el-table__body) {
    font-size: 14px;
  }
}

// 订单卡片样式
.analysis-order-card {
  margin-bottom: 24px;
}

// 分析内容容器样式
.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  
  & > * {
    margin-bottom: 0;
  }
}

// 移除了订单过滤器样式

// 订单表格容器样式
.orders-table-container {
  .table-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #303133;
    display: flex;
    align-items: center;
    
    &:before {
      content: "";
      display: inline-block;
      width: 3px;
      height: 16px;
      background-color: #409EFF;
      margin-right: 8px;
    }
  }
  
  .orders-table {
    margin-bottom: 15px;
    
    :deep(.el-table__header) {
      background-color: #f5f7fa;
    }
    
    :deep(.el-table--small) {
      font-size: 14px;
    }
    
    :deep(.el-table__body-wrapper) {
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
      }
    }
  }
}

.order-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    background-color: #f9fafc;
  }
  
  :deep(.el-card__body) {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f5f7fa;
    }
  }
}

.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  .order-title {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    
    .order-tag {
      margin-right: 12px;
    }
    
    .order-no {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .order-product {
    font-size: 14px;
    color: #606266;
  }
}

.order-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  background-color: #f9fafc;
  padding: 12px;
  border-radius: 4px;
  
  .info-item {
    margin-right: 24px;
    margin-bottom: 10px;
    min-width: 120px;
    
    .info-label {
      font-size: 12px;
      color: #909399;
      margin-bottom: 4px;
    }
    
    .info-value {
      font-size: 14px;
      color: #303133;
      
      &.highlight {
        font-weight: bold;
        color: #409EFF;
      }
      
      &.remark {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.detail-table-container {
  .table-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #303133;
    display: flex;
    align-items: center;
    
    &:before {
      content: "";
      display: inline-block;
      width: 3px;
      height: 16px;
      background-color: #409EFF;
      margin-right: 8px;
    }
  }
}

.detail-table {
  :deep(.el-table__header) {
    background-color: #f5f7fa;
  }

  :deep(.el-table--small) {
    font-size: 14px;
  }

  :deep(.el-table__body) {
    font-size: 14px;
  }
}

// 适配中文响应式布局
:deep(.el-dialog__body) {
  overflow-x: hidden;
}

.quantity-editor {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .unit-text {
    margin-left: 5px;
    color: #909399;
  }
}

.quantity-display {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .unit-text {
    margin-left: 5px;
    color: #909399;
    font-size: 12px;
  }
}



.remark-text {
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  justify-content: center;
  
  .el-button {
    padding: 4px 8px;
    margin: 0 3px;
  }
}

// 移除了物料明细面板样式



.font-bold {
  font-weight: bold;
}

.mb-5px {
  margin-bottom: 5px;
}

.mb-10px {
  margin-bottom: 10px;
}

.mb-15px {
  margin-bottom: 15px;
}

.mt-15px {
  margin-top: 15px;
}

.ml-5px {
  margin-left: 5px;
}
</style>
