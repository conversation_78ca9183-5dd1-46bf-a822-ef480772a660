import request from '@/config/axios'
import qs from 'qs'

// 物料信息 VO
export interface MaterialVO {
  id: number // 主键
  code: string // 物料编码
  categoryCode: string // 分类编号
  fullCode: string // 物料编码全称
  type: string // 分类
  form: string // 形态
  subType: string // 子分类
  name: string // 物料名称
  fullName: string // 物料全称
  spec: string // 物料规格
  specQuantity: string // 物料规格数量
  specUnit: string // 物料规格单位
  unit: string // 主单位
  auxiliaryUnit: string // 辅助单位
  purchasePrice: number // 采购价格
  salePrice: number // 销售价格
  averagePurchasePrice: number // 加权采购价格
  priceUnit: string // 价格单位
  elements: Array<any> // 元素组成
  url: string // 物料图片地址
  isClientProvided: boolean // 客供
  remark: string // 备注
  otherInfo: JSON // 其他信息
  inventoryAccountId: number // 存货科目
  saleAccountId: number // 销售收入科目
  saleCostAccountId: number // 销售成本科目
  costDiffAccountId: number // 成本差异科目
  adminAccountId: number // 代管科目
  rateAccountId: number // 税目科目
  costProjectId: number // 成本项目
  qrCode: string // 二维码
  saleUnit:number // 销售单位
  purchaseUnit: number // 采购单位
  mfgUnit: number // ufactor 制造单位
  inventoryUnit: number // 库存单位
  max: number // 最大库存
  min: number // 最小库存
  source: string // 来源
}

// 物料信息 API
export const MaterialApi = {
  // 查询物料信息分页
  getMaterialPage: async (params: any) => {
    return await request.get({ url: `/scm/base/material/page`, params })
  },

  // 查询物料信息详情
  getMaterial: async (id: number) => {
    return await request.get({ url: `/scm/base/material/get?id=` + id })
  },

  // 新增物料信息
  createMaterial: async (data: MaterialVO) => {
    return await request.post({ url: `/scm/base/material/create`, data })
  },

  // 修改物料信息
  updateMaterial: async (data: MaterialVO) => {
    return await request.put({ url: `/scm/base/material/update`, data })
  },

  // 删除物料信息
  deleteMaterial: async (id: number) => {
    return await request.delete({ url: `/scm/base/material/delete?id=` + id })
  },

  // 导出物料信息 Excel
  exportMaterial: async (params) => {
    return await request.download({ url: `/scm/base/material/export-excel`, params })
  },

  // 查询物料基本信息列表
  getSimpleMaterialPage: async (params: any) => {
    let url  = '/scm/base/material/simple-page'
    if(params) {
      url += '?' + qs.stringify(params, { arrayFormat: 'repeat' })
    }
    return await request.get({ url: url})
  },
  // 初始化到es
  initToEs: async () => {
    return await request.put({ url: `/scm/base/material/init-to-es`})
  },
  //获得物料基本信息
  getMaterialSimpleInfo: async (params:any) => {
    return await request.get({url:`scm/base/material/get-simple`,params})
  }
}
